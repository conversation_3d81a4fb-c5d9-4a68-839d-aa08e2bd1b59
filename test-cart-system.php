<?php
/**
 * ملف اختبار نظام السلة المحدث
 * 
 * هذا الملف يحتوي على اختبارات للتأكد من أن النظام المحدث يعمل بشكل صحيح
 * ولا ينشئ طلبات مكررة عند إضافة للسلة أو إتمام الطلب
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * كلاس اختبار نظام السلة
 */
class Pexlat_Form_Cart_System_Test {
    
    /**
     * تشغيل جميع الاختبارات
     */
    public static function run_all_tests() {
        echo "<h2>اختبار نظام السلة المحدث</h2>";
        
        // اختبار 1: التحقق من وجود الدوال الجديدة
        self::test_new_functions_exist();
        
        // اختبار 2: اختبار إنشاء طلب مسودة
        self::test_draft_order_creation();
        
        // اختبار 3: اختبار البحث عن طلب مسودة موجود
        self::test_existing_draft_order_search();
        
        // اختبار 4: اختبار تنظيف الطلبات القديمة
        self::test_old_draft_cleanup();
        
        echo "<h3>انتهت جميع الاختبارات</h3>";
    }
    
    /**
     * اختبار وجود الدوال الجديدة
     */
    private static function test_new_functions_exist() {
        echo "<h3>اختبار 1: التحقق من وجود الدوال الجديدة</h3>";
        
        if (!class_exists('Pexlat_Form_Custom_Cart_System')) {
            echo "<p style='color: red;'>❌ كلاس نظام السلة غير موجود</p>";
            return;
        }
        
        $cart_system = new Pexlat_Form_Custom_Cart_System();
        $reflection = new ReflectionClass($cart_system);
        
        $required_methods = [
            'ensure_draft_order_exists',
            'get_or_create_draft_order_for_checkout',
            'cleanup_old_draft_orders',
            'cleanup_orphaned_draft_options',
            'schedule_draft_cleanup',
            'unschedule_draft_cleanup'
        ];
        
        foreach ($required_methods as $method) {
            if ($reflection->hasMethod($method)) {
                echo "<p style='color: green;'>✅ الدالة {$method} موجودة</p>";
            } else {
                echo "<p style='color: red;'>❌ الدالة {$method} غير موجودة</p>";
            }
        }
    }
    
    /**
     * اختبار إنشاء طلب مسودة
     */
    private static function test_draft_order_creation() {
        echo "<h3>اختبار 2: اختبار إنشاء طلب مسودة</h3>";
        
        // محاكاة بيانات العميل
        $customer_data = [
            'phone' => '0555123456',
            'full_name' => 'اختبار العميل',
            'address' => 'عنوان اختبار'
        ];
        
        // محاكاة إنشاء طلب مسودة
        $order = wc_create_order(['status' => 'draft']);
        
        if ($order && !is_wp_error($order)) {
            echo "<p style='color: green;'>✅ تم إنشاء طلب مسودة بنجاح - ID: " . $order->get_id() . "</p>";
            
            // حفظ معلومات المسودة
            $draft_key = 'pexlat_form_draft_' . md5($customer_data['phone']);
            $draft_info = [
                'order_id' => $order->get_id(),
                'created' => current_time('mysql'),
                'updated' => current_time('mysql'),
            ];
            
            update_option($draft_key, $draft_info, false);
            echo "<p style='color: green;'>✅ تم حفظ معلومات المسودة بنجاح</p>";
            
            // تنظيف الاختبار
            wp_delete_post($order->get_id(), true);
            delete_option($draft_key);
            echo "<p style='color: blue;'>🧹 تم تنظيف بيانات الاختبار</p>";
            
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء طلب مسودة</p>";
        }
    }
    
    /**
     * اختبار البحث عن طلب مسودة موجود
     */
    private static function test_existing_draft_order_search() {
        echo "<h3>اختبار 3: اختبار البحث عن طلب مسودة موجود</h3>";
        
        $phone = '0555987654';
        $draft_key = 'pexlat_form_draft_' . md5($phone);
        
        // إنشاء طلب مسودة للاختبار
        $order = wc_create_order(['status' => 'draft']);
        
        if ($order && !is_wp_error($order)) {
            // حفظ معلومات المسودة
            $draft_info = [
                'order_id' => $order->get_id(),
                'created' => current_time('mysql'),
                'updated' => current_time('mysql'),
            ];
            
            update_option($draft_key, $draft_info, false);
            
            // اختبار البحث
            $existing_draft = get_option($draft_key);
            
            if ($existing_draft && isset($existing_draft['order_id'])) {
                $found_order = wc_get_order($existing_draft['order_id']);
                
                if ($found_order && $found_order->get_status() == 'draft') {
                    echo "<p style='color: green;'>✅ تم العثور على الطلب المسودة الموجود بنجاح - ID: " . $found_order->get_id() . "</p>";
                } else {
                    echo "<p style='color: red;'>❌ الطلب المسودة الموجود غير صالح</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ لم يتم العثور على معلومات المسودة</p>";
            }
            
            // تنظيف الاختبار
            wp_delete_post($order->get_id(), true);
            delete_option($draft_key);
            echo "<p style='color: blue;'>🧹 تم تنظيف بيانات الاختبار</p>";
            
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء طلب مسودة للاختبار</p>";
        }
    }
    
    /**
     * اختبار تنظيف الطلبات القديمة
     */
    private static function test_old_draft_cleanup() {
        echo "<h3>اختبار 4: اختبار تنظيف الطلبات القديمة</h3>";
        
        // إنشاء طلب مسودة قديم للاختبار
        $order = wc_create_order(['status' => 'draft']);
        
        if ($order && !is_wp_error($order)) {
            // تعديل تاريخ الطلب ليكون قديماً (أكثر من 7 أيام)
            $old_date = date('Y-m-d H:i:s', strtotime('-8 days'));
            wp_update_post([
                'ID' => $order->get_id(),
                'post_date' => $old_date,
                'post_date_gmt' => get_gmt_from_date($old_date)
            ]);
            
            // إضافة معرف النموذج للطلب
            $order->update_meta_data('_pexlat_form_form_id', 1);
            $order->save();
            
            echo "<p style='color: blue;'>📅 تم إنشاء طلب مسودة قديم للاختبار - ID: " . $order->get_id() . "</p>";
            
            // اختبار دالة التنظيف
            if (class_exists('Pexlat_Form_Custom_Cart_System')) {
                $cart_system = new Pexlat_Form_Custom_Cart_System();
                $deleted_count = $cart_system->cleanup_old_draft_orders();
                
                echo "<p style='color: green;'>✅ تم تشغيل دالة التنظيف - تم حذف {$deleted_count} طلب</p>";
                
                // التحقق من حذف الطلب
                $check_order = wc_get_order($order->get_id());
                if (!$check_order) {
                    echo "<p style='color: green;'>✅ تم حذف الطلب القديم بنجاح</p>";
                } else {
                    echo "<p style='color: red;'>❌ لم يتم حذف الطلب القديم</p>";
                    // تنظيف يدوي
                    wp_delete_post($order->get_id(), true);
                }
            } else {
                echo "<p style='color: red;'>❌ كلاس نظام السلة غير متاح</p>";
                // تنظيف يدوي
                wp_delete_post($order->get_id(), true);
            }
            
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء طلب مسودة للاختبار</p>";
        }
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (isset($_GET['run_cart_tests']) && $_GET['run_cart_tests'] == '1') {
    // التأكد من أن WooCommerce متاح
    if (class_exists('WooCommerce')) {
        Pexlat_Form_Cart_System_Test::run_all_tests();
    } else {
        echo "<p style='color: red;'>❌ WooCommerce غير متاح</p>";
    }
}
?>
