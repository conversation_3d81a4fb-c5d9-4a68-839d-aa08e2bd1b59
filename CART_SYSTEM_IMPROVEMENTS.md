# تحسينات نظام السلة - حل مشكلة الطلبات المتروكة

## المشاكل التي تم حلها

### 1. إنشاء طلبات متعددة عند الطلب من السلة
**المشكلة**: كان النظام ينشئ طلباً جديداً عند إتمام الطلب من السلة بدلاً من تحديث الطلب المسودة الموجود.

**الحل**: 
- تم تعديل دالة `handle_complete_order()` للبحث عن الطلب المسودة الموجود أولاً
- إضافة دالة `get_or_create_draft_order_for_checkout()` للبحث الذكي عن الطلبات المسودة
- تحديث الطلب الموجود بدلاً من إنشاء طلب جديد

### 2. إنشاء طلبات متعددة عند إضافة للسلة والانتقال بين الصفحات
**المشكلة**: كان النظام ينشئ طلب مسودة جديد في كل مرة يضيف فيها المستخدم منتجاً للسلة.

**الحل**:
- تم تعديل دالة `handle_add_to_cart()` لتتحقق من وجود طلب مسودة موجود
- إضافة دالة `ensure_draft_order_exists()` للبحث عن الطلبات المسودة وإنشاؤها عند الحاجة فقط
- ربط الطلبات المسودة برقم الهاتف أو معرف الجلسة لتجنب التكرار

## التحسينات المضافة

### 1. نظام البحث الذكي عن الطلبات المسودة
```php
// البحث بناءً على رقم الهاتف أولاً
if (!empty($phone)) {
    $draft_key = 'pexlat_form_draft_' . md5($phone);
    $existing_draft = get_option($draft_key);
}

// البحث بناءً على معرف الجلسة كبديل
if (!$existing_draft_id && isset($_COOKIE['pexlat_form_session'])) {
    $session_id = sanitize_text_field($_COOKIE['pexlat_form_session']);
    $draft_key = 'pexlat_form_draft_' . $session_id;
}
```

### 2. آلية تنظيف الطلبات المسودة القديمة
- **دالة `cleanup_old_draft_orders()`**: تحذف الطلبات المسودة الأقدم من 7 أيام
- **دالة `cleanup_orphaned_draft_options()`**: تنظف خيارات المسودات اليتيمة
- **جدولة يومية**: تشغيل التنظيف تلقائياً كل يوم
- **تنظيف عند إلغاء التفعيل**: إزالة الجدولة عند إلغاء تفعيل الإضافة

### 3. تحسين إدارة الجلسات
- استخدام كوكيز للتتبع عبر الصفحات
- ربط الطلبات المسودة بمعرفات فريدة
- تنظيف المراجع المعطلة تلقائياً

## الملفات المعدلة

### 1. `includes/class-custom-cart-system.php`
- **دوال جديدة**:
  - `ensure_draft_order_exists()`: التأكد من وجود طلب مسودة
  - `get_or_create_draft_order_for_checkout()`: البحث عن طلب مسودة لإتمام الطلب
  - `cleanup_old_draft_orders()`: تنظيف الطلبات القديمة
  - `cleanup_orphaned_draft_options()`: تنظيف الخيارات اليتيمة
  - `schedule_draft_cleanup()`: جدولة التنظيف
  - `unschedule_draft_cleanup()`: إلغاء الجدولة

- **تعديلات على الدوال الموجودة**:
  - `handle_add_to_cart()`: إضافة استدعاء `ensure_draft_order_exists()`
  - `handle_complete_order()`: استخدام `get_or_create_draft_order_for_checkout()`

### 2. `includes/class-pexlat-form-deactivator.php`
- إضافة دالة `cleanup_scheduled_events()` لإلغاء الجدولة عند إلغاء التفعيل

## كيفية الاختبار

### 1. اختبار يدوي
1. **إضافة للسلة**:
   - املأ النموذج وأضف منتجاً للسلة
   - انتقل لصفحة أخرى ثم عد
   - أضف منتجاً آخر للسلة
   - تحقق من عدم إنشاء طلبات متعددة في لوحة WooCommerce

2. **إتمام الطلب من السلة**:
   - أضف منتجات للسلة
   - اذهب لصفحة السلة وأتمم الطلب
   - تحقق من أن الطلب الموجود تم تحديثه وليس إنشاء طلب جديد

### 2. اختبار تلقائي
```php
// تشغيل ملف الاختبار
include 'test-cart-system.php';
Pexlat_Form_Cart_System_Test::run_all_tests();
```

أو زيارة: `your-site.com/wp-content/plugins/pexlat-form/test-cart-system.php?run_cart_tests=1`

## السيناريوهات المختبرة

### ✅ السيناريو 1: إضافة للسلة والانتقال بين الصفحات
- المستخدم يملأ النموذج ويضيف للسلة
- ينتقل لصفحة أخرى
- يعود ويضيف منتجاً آخر
- **النتيجة**: طلب مسودة واحد فقط

### ✅ السيناريو 2: إتمام الطلب من السلة
- المستخدم يضيف منتجات للسلة
- يذهب لصفحة السلة ويتمم الطلب
- **النتيجة**: تحديث الطلب المسودة الموجود وليس إنشاء طلب جديد

### ✅ السيناريو 3: تنظيف الطلبات القديمة
- الطلبات المسودة الأقدم من 7 أيام يتم حذفها تلقائياً
- **النتيجة**: قاعدة بيانات نظيفة بدون طلبات متراكمة

## الفوائد

1. **تقليل الطلبات المكررة**: لا توجد طلبات متعددة لنفس المستخدم
2. **تحسين الأداء**: قاعدة بيانات أنظف وأسرع
3. **تجربة مستخدم أفضل**: عدم وجود طلبات مربكة في لوحة التحكم
4. **إدارة أسهل**: طلبات منظمة وواضحة للمدير

## ملاحظات مهمة

- التحسينات متوافقة مع النظام الحالي
- لا تؤثر على الطلبات الموجودة
- يمكن تعطيل تنظيف الطلبات المسودة من الإعدادات
- النظام يحافظ على البيانات المهمة ولا يحذف إلا الطلبات غير المكتملة

## الدعم والصيانة

- تسجيل مفصل للأخطاء في `error_log`
- رسائل واضحة للتصحيح
- آلية تنظيف آمنة مع حدود زمنية
- إمكانية إيقاف التنظيف التلقائي عند الحاجة
